using System.Linq.Expressions;
using EAMS.Domain.Entities;
using EAMS.Domain.Interfaces;
using Moq;
using Microsoft.Graph.Models;
using GraphUser = Microsoft.Graph.Models.User;
using User = EAMS.Domain.Aggregates.User;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using Microsoft.Extensions.Configuration;
using EAMS.Domain.Repositories;
using EAMS.Domain.Aggregates;
using EAMS.Application.Services;

namespace EAMS.Tests;

public class UserServicesTestFixture
{
    private Mock<IGraphService> _mockGraphService;
    private Mock<IUserRepository> _mockUserRepository;
    private Mock<IUserInvitationRepository> _mockUserInvitationRepository;
    private Mock<IOrganisationRepository> _mockOrganisationRepository;
    private Mock<ILogger<UserService>> _mockLogger;
    private Mock<IConfiguration> _mockConfiguration;
    private GraphUser _user;
    private Group _usersGroup;
    private Organisation _organisation;
    private UserService _targetService;

    public UserServicesTestFixture()
    {
        // Mock GraphServiceClient
        this._mockGraphService = new Mock<IGraphService>();
        this._user = new GraphUser()
        {
            Id = Guid.NewGuid().ToString(),
            DisplayName = "John Doe",
            GivenName = "John",
            Surname = "Doe",
            Mail = "<EMAIL>",
            CompanyName = "McAuleys",
            JobTitle = "Developer",
            MobilePhone = "0411 223 345",
        };
        this._mockGraphService
            .Setup(x => x.GetCurrentLoginUserAsync())
            .ReturnsAsync(this._user);

        this._usersGroup = new Group()
        {
            Id = Guid.NewGuid().ToString(),
            DisplayName = "Users"
        };

        this._mockGraphService
            .Setup(x => x.GetGroupByNameAsync("Users"))
            .ReturnsAsync(this._usersGroup);

        // Mock Dependencies
        this._mockUserRepository = new Mock<IUserRepository>();
        this._mockUserInvitationRepository = new Mock<IUserInvitationRepository>();
        this._mockOrganisationRepository = new Mock<IOrganisationRepository>();
        this._mockLogger = new Mock<ILogger<UserService>>();
        this._mockConfiguration = new Mock<IConfiguration>();

        // Setup Mock Organisation
        _organisation = new Organisation()
        {
            Id = Guid.NewGuid(),
            Name = "McAuleys",
        };

        this._mockOrganisationRepository
            .Setup(x => x.GetByIdAsync(_organisation.Id))
            .ReturnsAsync(this._organisation);

        // Setup Mock Configuration
        _mockConfiguration.Setup(config => config.GetSection("GraphApi:RolesMapping:Users").Value)
            .Returns("Users");

        this._targetService = new UserService(this._mockGraphService.Object,
            this._mockUserRepository.Object,
            this._mockUserInvitationRepository.Object,
            this._mockOrganisationRepository.Object,
            this._mockConfiguration.Object,
            this._mockLogger.Object);
    }

    [Fact]
    public async Task Test_GetCurrentLoginUserAsync_Should_Retrieve_Additional_Data_From_UserRepo()
    {
        // arrange
        var dbUser = new User()
        {
            Id = Guid.Parse(_user.Id),
        };
        this._mockUserRepository
            .Setup(x => x.GetByIdAsync(It.IsAny<Guid>())).ReturnsAsync(dbUser);


        // act
        var result = await _targetService.GetCurrentLoginUserAsync();

        // assert
        this._mockUserRepository.Verify(repo => repo.GetByIdAsync(It.IsAny<Guid>(), usr => usr.Organisation), Times.Once);
        Assert.Equal(result.Id, Guid.Parse(this._user.Id));
        Assert.Equal(result.GraphUser, _user);
    }

    [Fact]
    public async Task Test_GetCurrentLoginUserAsync_UserDoesNotExistsInUserTable_Should_Check_Existing_Invitation_AndCreate_NewUserRecord()
    {
        // arrange
        this._mockUserInvitationRepository
            .Setup(repo => repo.GetAllAsync(It.IsAny<Expression<Func<UserInvitation, bool>>>(),
                It.IsAny<Expression<Func<UserInvitation, object>>>()))
            .ReturnsAsync(new List<UserInvitation>() {new UserInvitation()
            {
                Id = Guid.NewGuid(),
                TargetOrganisationId = _organisation.Id,
                TargetOrganisation = _organisation,
            }});

        // act
        var result = await _targetService.GetCurrentLoginUserAsync();

        // assert
        Assert.NotNull(result);
        Assert.Equal(this._organisation.Id, result.OrganisationId);
        this._mockUserRepository.Verify(repo => repo.AddAsync(It.IsAny<User>()), Times.Once);
    }

    [Fact]
    public async Task Test_CreateInvitationAsync_Should_AddUserToGroup_CreateNewUserInvitation_ReturnsValidId()
    {
        // arrange
        var userInvitation = new UserInvitation()
        {
            InvitedUserEmailAddress = "<EMAIL>",
            TargetOrganisationId = this._organisation.Id,
            Roles = "Users"
        };

        Invitation mockInvitation = new Invitation()
        {
            Id = Guid.NewGuid().ToString(),
            InvitedUser = new GraphUser()
            {
                Id = Guid.NewGuid().ToString()
            }
        };

        _mockGraphService
            .Setup(x => x.InviteUserAsync(userInvitation.InvitedUserEmailAddress, It.IsAny<string>()))
            .ReturnsAsync(mockInvitation);

        // act
        var result = await _targetService.CreateInvitationAsync(userInvitation);

        // assert
        Assert.Equal(Guid.Parse(mockInvitation.Id), result.Id);
        this._mockUserInvitationRepository.Verify(repo => repo.AddAsync(It.IsAny<UserInvitation>()), Times.Once);
        this._mockGraphService.Verify(repo => repo.GetCurrentLoginUserAsync(), Times.Once);
        this._mockGraphService.Verify(repo => repo.GetGroupByNameAsync(userInvitation.Roles), Times.Once);
        this._mockGraphService.Verify(repo => repo.AddUserToGroupAsync(Guid.Parse(mockInvitation.InvitedUser.Id),
            Guid.Parse(_usersGroup.Id)), Times.Once);
    }

    [Fact]
    public async Task Test_CreateInvitationAsync_WithExistingInvitation_ShouldResendInvitation_AndUpdateInvitationRecord()
    {
        // arrange
        GraphUser userToInvite = new GraphUser()
        {
            Id = Guid.NewGuid().ToString(),
            Mail = "<EMAIL>"
        };

        UserInvitation existingInvitation = new UserInvitation()
        {
            Id = Guid.NewGuid(),
            TargetOrganisationId = _organisation.Id,
            InvitedByUserId = Guid.Parse(_user.Id),
            InvitedUserId = Guid.Parse(userToInvite.Id),
            InvitedUserEmailAddress = userToInvite.Mail,
        };

        Invitation mockInvitation = new Invitation()
        {
            Id = existingInvitation.Id.ToString(),
            InvitedUser = new GraphUser()
            {
                Id = userToInvite.Id
            }
        };

        this._mockUserInvitationRepository.Setup(repo => repo.GetByIdAsync(It.IsAny<Guid>()))
            .ReturnsAsync(existingInvitation);

        this._mockGraphService
            .Setup(x => x.InviteUserAsync(userToInvite.Mail, It.IsAny<string>()))
            .ReturnsAsync(mockInvitation);

        // act
        var result = await _targetService.CreateInvitationAsync(existingInvitation);

        // assert
        Assert.Equal(existingInvitation.Id, result.Id);
        _mockUserInvitationRepository.Verify(repo => repo.UpdateAsync(It.IsAny<UserInvitation>()), Times.Once);
        _mockGraphService.Verify(repo => repo.AddUserToGroupAsync(It.IsAny<Guid>(), It.IsAny<Guid>()), Times.Never);
    }

    [Fact]
    public async Task Test_UpdateUserDetailsAsync_Should_Call_UserRepo_Update_When_OrganisationChanges()
    {
        // arrange
        var updatedDetails = JsonSerializer.Deserialize<GraphUser>(
            JsonSerializer.Serialize(this._user));

        updatedDetails.CompanyName = "New Company";
        updatedDetails.DisplayName = "John Updated";
        updatedDetails.Surname = "Updated";

        _mockGraphService.Setup(s => s.PatchUserDetailsAsync(It.IsAny<GraphUser>()))
            .ReturnsAsync(updatedDetails);

        _mockOrganisationRepository.Setup(repo => repo.GetOrganisationByNameAsync(updatedDetails.CompanyName))
            .ReturnsAsync(new Organisation()
            {
                Id = Guid.NewGuid(),
                Name = updatedDetails.CompanyName
            });

        _mockUserRepository.Setup(repo => repo.GetByIdAsync(It.IsAny<Guid>()))
            .ReturnsAsync(new User()
            {
                Id = Guid.Parse(_user.Id),
                OrganisationId = _organisation.Id,
                Organisation = _organisation,
            });

        // act
        await _targetService.UpdateUserDetailsAsync(updatedDetails);

        // assert
        _mockGraphService.Verify(svc => svc.PatchUserDetailsAsync(It.IsAny<GraphUser>()), Times.Once);
        _mockUserRepository.Verify(repo => repo.UpdateAsync(It.IsAny<User>()), Times.Once);
    }

    [Fact]
    public async Task Test_UpdateUserDetailsAsync_ShouldNot_Call_UserRepo_Update_When_OrganisationDoesNotChange()
    {
        // arrange
        var updatedDetails = JsonSerializer.Deserialize<GraphUser>(
            JsonSerializer.Serialize(this._user));

        updatedDetails.DisplayName = "John Updated";
        updatedDetails.Surname = "Updated";
        updatedDetails.CompanyName = _organisation.Name;

        _mockGraphService.Setup(s => s.PatchUserDetailsAsync(It.IsAny<GraphUser>()))
            .ReturnsAsync(updatedDetails);

        _mockOrganisationRepository.Setup(repo => repo.GetOrganisationByNameAsync(updatedDetails.CompanyName))
            .ReturnsAsync(new Organisation()
            {
                Id = _organisation.Id,
                Name = _organisation.Name
            });

        _mockUserRepository.Setup(repo => repo.GetByIdAsync(It.IsAny<Guid>()))
            .ReturnsAsync(new User()
            {
                Id = Guid.Parse(_user.Id),
                OrganisationId = _organisation.Id,
                Organisation = _organisation,
            });

        // act
        await _targetService.UpdateUserDetailsAsync(updatedDetails);

        // assert
        _mockGraphService.Verify(svc => svc.PatchUserDetailsAsync(It.IsAny<GraphUser>()), Times.Once);
        _mockUserRepository.Verify(repo => repo.UpdateAsync(It.IsAny<User>()), Times.Never);
    }

    [Fact]
    public async Task Test_GetUserByIdAsync_Should_Return_Null_When_GraphUser_NotFound()
    {
        // arrange
        _mockGraphService.Setup(svc => svc.GetUserByIdAsync(It.IsAny<Guid>()))
            .ReturnsAsync((GraphUser)null);

        // act
        var result = await _targetService.GetUserByIdAsync(Guid.NewGuid());

        // assert
        Assert.Null(result);
        _mockGraphService.Verify(svc => svc.GetUserByIdAsync(It.IsAny<Guid>()), Times.Once);
        _mockUserRepository.Verify(svc => svc.GetByIdAsync(It.IsAny<Guid>(), usr => usr.Organisation), Times.Never);
    }

    [Fact]
    public async Task Test_GetUserByIdAsync_Should_Return_Null_When_UserRecord_NotFound()
    {
        // arrange
        _mockGraphService.Setup(svc => svc.GetUserByIdAsync(It.IsAny<Guid>()))
            .ReturnsAsync(this._user);
        _mockUserRepository.Setup(repo => repo.GetByIdAsync(It.IsAny<Guid>(), usr => usr.Organisation))
            .ReturnsAsync((User)null);

        // act 
        var result = await _targetService.GetUserByIdAsync(Guid.Parse(this._user.Id));

        // assert
        Assert.Null(result);
        _mockGraphService.Verify(svc => svc.GetUserByIdAsync(It.IsAny<Guid>()), Times.Once);
        _mockUserRepository.Verify(svc => svc.GetByIdAsync(It.IsAny<Guid>(), usr => usr.Organisation), Times.Once);
    }

    [Fact]
    public async Task Test_AddUserToGroupAsync_Should_Call_GraphService_AddUserToGroupAsync()
    {
        // arrange
        _mockGraphService.Setup(svc => svc.GetUserByIdAsync(Guid.Parse(this._user.Id)))
            .ReturnsAsync(this._user);
        _mockGraphService.Setup(svc => svc.GetGroupByNameAsync(this._usersGroup.DisplayName))
            .ReturnsAsync(this._usersGroup);
        _mockGraphService.Setup(svc => svc.GetCurrentLoginUserAsync())
            .ReturnsAsync(new GraphUser
            {
                Id = Guid.NewGuid().ToString(),
                DisplayName = "Admin User",
            });

        // act
        await _targetService.AddUserToGroupAsync(Guid.Parse(_user.Id), _usersGroup.DisplayName);

        // assert
        _mockGraphService.Verify(svc => svc.AddUserToGroupAsync(Guid.Parse(_user.Id), Guid.Parse(_usersGroup.Id)), Times.Once);
    }

    [Fact]
    public async Task Test_AddUserToGroupAsync_Should_ThrowError_WhenUser_NotFound()
    {
        // arrange
        _mockGraphService.Setup(svc => svc.GetUserByEmailAsync(this._user.Mail))
            .ReturnsAsync((GraphUser)null);
        _mockGraphService.Setup(svc => svc.GetGroupByNameAsync(this._usersGroup.DisplayName))
            .ReturnsAsync(this._usersGroup);
        _mockGraphService.Setup(svc => svc.GetCurrentLoginUserAsync())
            .ReturnsAsync(new GraphUser
            {
                Id = Guid.NewGuid().ToString(),
                DisplayName = "Admin User",
            });

        // act & assert
        Assert.ThrowsAsync<InvalidDataException>(() => _targetService.AddUserToGroupAsync(Guid.Parse(_user.Id), _usersGroup.DisplayName)); 
    }

    [Fact]
    public async Task Test_AddUserToGroupAsync_Should_ThrowError_WhenGroup_NotFound()
    {
        // arrange
        _mockGraphService.Setup(svc => svc.GetUserByEmailAsync(this._user.Mail))
            .ReturnsAsync(this._user);
        _mockGraphService.Setup(svc => svc.GetGroupByNameAsync(this._usersGroup.DisplayName))
            .ReturnsAsync((Group)null);
        _mockGraphService.Setup(svc => svc.GetCurrentLoginUserAsync())
            .ReturnsAsync(new GraphUser
            {
                Id = Guid.NewGuid().ToString(),
                DisplayName = "Admin User",
            });

        // act & assert
        Assert.ThrowsAsync<InvalidDataException>(() => _targetService.AddUserToGroupAsync(Guid.Parse(_user.Id), _usersGroup.DisplayName));
    }
}