using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Identity.Web.Resource;
using EAMS.Application.DTOs;
using EAMS.Application.Interfaces;
using EAMS.Domain.Exceptions;
using AutoMapper;
using EAMS.Domain.Aggregates;

namespace EAMS.API.Controllers;

[Authorize]
[ApiController]
[RequiredScope(RequiredScopesConfigurationKey = "AzureAd:Scopes:Organisations")]
[Route("api/[controller]")]
public class OrganisationsController : ControllerBase
{
    private readonly IOrganisationService _organisationService;
    private readonly IUserService _userService;
    private readonly ILogger<OrganisationsController> _logger;
    private readonly IMapper _mapper;

    public OrganisationsController(
        IOrganisationService organisationService,
        IUserService userService,
        ILogger<OrganisationsController> logger,
        IMapper mapper)
    {
        _organisationService = organisationService;
        _userService = userService;
        _logger = logger;
        _mapper = mapper;
    }

    /// <summary>
    /// Get all organisations
    /// </summary>
    /// <remarks>
    /// This end point is restricted to users with the "Administrators" role.
    /// </remarks>
    [HttpGet]
    [Authorize(Roles = "Administrators")]
    public async Task<ActionResult<IEnumerable<OrganisationDetailsDto>>> GetOrganisations()
    {
        var organisations = await _organisationService.GetAll();
        return Ok(_mapper.Map<IEnumerable<OrganisationDetailsDto>>(organisations));
    }

    /// <summary>
    /// Get organisation by ID
    /// </summary>
    [HttpGet("{id}")]
    [Authorize(Roles = "Users")]
    public async Task<ActionResult<OrganisationDetailsDto>> GetOrganisation(Guid id)
    {
        if (!await UserCanReadOrganisation(id))
        {
            return Unauthorized();
        }

        var organisation = await _organisationService.GetById(id);

        if (organisation == null)
        {
            throw new EntityNotFoundException("Organisation", id);
        }

        return Ok(_mapper.Map<OrganisationDetailsDto>(organisation));
    }

    /// <summary>
    /// Create a new organisation
    /// </summary>
    [HttpPost]
    [Authorize(Roles = "Administrators, Managers")]
    public async Task<ActionResult<OrganisationDetailsDto>> CreateOrganisation(CreateOrganisationDto organisationDto)
    {
        if (HttpContext.User.IsInRole("Administrators") || (organisationDto.ParentOrgId.HasValue && await UserCanReadOrganisation(organisationDto.ParentOrgId.Value)))
        {
            var organisation = _mapper.Map<Organisation>(organisationDto);
            var createdOrganisation = await _organisationService.Create(organisation);

            return CreatedAtAction(nameof(GetOrganisation), new { id = createdOrganisation.Id }, createdOrganisation);
        }
        else return Unauthorized();
    }

    /// <summary>
    /// Update an existing organisation
    /// </summary>
    [HttpPut("{id}")]
    [Authorize(Roles = "Administrators, Managers")]
    public async Task<ActionResult<OrganisationDetailsDto>> UpdateOrganisation(Guid id, UpdateOrganisationDto organisationDto)
    {
        if (await UserCanReadOrganisation(organisationDto.Id) == false)
            return Unauthorized();
        var organisation = _mapper.Map<Organisation>(organisationDto);
        var updatedOrganisation = await _organisationService.Update(organisation);
        var response = _mapper.Map<OrganisationDetailsDto>(updatedOrganisation);

        return Ok(response);
    }

    /// <summary>
    /// Delete an organisation
    /// </summary>
    [HttpDelete("{id}")]
    [Authorize(Roles = "Administrators, Managers")]
    public async Task<ActionResult<bool>> DeleteOrganisation(Guid id)
    {
        if (await UserCanReadOrganisation(id) == false)
            return Unauthorized();

        var result = await _organisationService.Delete(id);
        return Ok(result);
    }


    /// <summary>
    /// Get all users in an organisation and its sub-organisations
    /// </summary>
    /// <param name="id">Organisation Id</param>
    /// <returns>List of users of link to the organisation</returns>
    [HttpGet("{id}/users")]
    [Authorize(Roles = "Administrators, Managers")]
    public async Task<ActionResult<IEnumerable<UserDto>>> GetOrganisationUsers(Guid id)
    {
        // check if organisation exists
        if (await UserCanReadOrganisation(id) == false)
            return Unauthorized();

        var orgUsers = await _organisationService.GetOrganisationUsers(id);
        return Ok(_mapper.Map<IEnumerable<UserDto>>(orgUsers));
    }

    /// <summary>
    /// Get all sub-organisations of an organisation
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpGet("{id}/suborganisations")]
    public async Task<ActionResult<IEnumerable<OrganisationDetailsDto>>> GetSubOrganisations(Guid id)
    {
        if (await UserCanReadOrganisation(id) == false)
        {
            return Unauthorized();
        }

        var subOrgs = await _organisationService.GetSubOrganisations(id);
        return Ok(_mapper.Map<IEnumerable<OrganisationDetailsDto>>(subOrgs));
    }

    /// <summary>
    /// Search  organisations with pagination and sorting
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    [Authorize(Roles = "Administrators")]
    [HttpGet("search")]
    public async Task<ActionResult<SearchResultDto<OrganisationDetailsDto>>> SearchOrganisations([FromQuery] SearchRequestDto request)
    {
        if (request.ValidateQuery() == false)
            return BadRequest("Invalid query parameters.");

        if (request.Sorting is null)
            request.Sorting = new SearchSorting { OrderBy = "name", Direction = "asc" };

        var (results, totalCount) = await _organisationService.SearchOrganisationAsync(request.SearchTerm,
            request.Pagination.Page, request.Pagination.PageSize, request.Sorting.OrderBy, request.Sorting.Direction);

        var resultDtos = _mapper.Map<IEnumerable<OrganisationDetailsDto>>(results);

        var response = new SearchResultDto<OrganisationDetailsDto>(_mapper.Map<IEnumerable<OrganisationDetailsDto>>(results),
            request.Pagination.Page, request.Pagination.PageSize, totalCount, request.Sorting.OrderBy, request.Sorting.Direction);

        return Ok(response);
    }

    private async Task<bool> UserCanReadOrganisation(Guid guid)
    {
        // get current user
        var userContext = HttpContext.User;
        if (userContext.IsInRole("Administrators"))
            return true;

        return await _organisationService.CanReadOrganisationAsync(guid);
    }
}
