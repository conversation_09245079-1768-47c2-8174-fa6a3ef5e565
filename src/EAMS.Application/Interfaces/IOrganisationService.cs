using EAMS.Domain.Aggregates;

namespace EAMS.Application.Interfaces;

public interface IOrganisationService
{
    Task<IEnumerable<Organisation>> GetAll();
    Task<IEnumerable<Organisation>> GetSubOrganisations(Guid parentOrgId);
    Task<Organisation?> GetById(Guid id);
    Task<Organisation> Create(Organisation organisation);
    Task<Organisation> Update(Organisation organisation);
    Task<bool> Delete(Guid id);
    Task<List<User>> GetOrganisationUsers(Guid orgId);
    Task<bool> CanReadOrganisationAsync(Guid orgId);
    Task<(List<Organisation> results, int totalCount)> SearchOrganisationAsync(string? searchTerm, 
        int pageNumber, int pageSize, string sortBy, string sortDirection);
}
