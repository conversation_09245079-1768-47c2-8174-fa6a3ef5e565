using EAMS.Application.Interfaces;
using EAMS.Domain.Exceptions;
using Microsoft.Extensions.Logging;
using System.Linq.Expressions;
using LinqKit;
using EAMS.Domain.Repositories;
using EAMS.Domain.Aggregates;

namespace EAMS.Application.Services;

public class OrganisationService : IOrganisationService
{
    private readonly IOrganisationRepository _organisationRepository;
    private readonly ILogger<OrganisationService> _logger;
    private readonly IUserService _userService;

    public OrganisationService(IOrganisationRepository organisationRepository,
        IUserService userSvc,
        ILogger<OrganisationService> logger)
    {
        _organisationRepository = organisationRepository;
        _logger = logger;
        _userService = userSvc;
    }

    public async Task<IEnumerable<Organisation>> GetAll()
    {
        return await _organisationRepository.GetAllAsync(null, o => o.ParentOrg);
    }

    public async Task<Organisation?> GetById(Guid id)
    {
        return await _organisationRepository.GetByIdAsync(id, includes: org => org.ParentOrg);
    }

    public async Task<Organisation> Create(Organisation organisation)
    {
        // Set timestamps for new entity
        organisation.CreatedAt = DateTime.UtcNow;
        organisation.UpdatedAt = DateTime.UtcNow;

        // AddAsync returns void and handles SaveChanges internally
        await _organisationRepository.AddAsync(organisation);

        // Return the organisation with its generated ID
        return organisation;
    }

    public async Task<Organisation> Update(Organisation organisation)
    {
        // Check if organisation exists first
        var existingOrganisation = await _organisationRepository.GetByIdAsync(organisation.Id);
        if (existingOrganisation == null)
        {
            throw new EntityNotFoundException("Organisation", organisation.Id);
        }

        // Update timestamp
        organisation.UpdatedAt = DateTime.UtcNow;
        // Preserve original creation timestamp
        organisation.CreatedAt = existingOrganisation.CreatedAt;

        // UpdateAsync returns void and handles SaveChanges internally
        await _organisationRepository.UpdateAsync(organisation);

        return organisation;
    }

    public async Task<bool> Delete(Guid id)
    {
        // Check if organisation exists first
        var exists = await _organisationRepository.GetByIdAsync(id);
        if (exists == null)
        {
            return false;
        }

        // DeleteAsync returns void and handles SaveChanges internally
        await _organisationRepository.DeleteAsync(id);

        return true;
    }

    public async Task<List<User>> GetOrganisationUsers(Guid orgId)
    {
        // get the organisations and its children
        var organisation = await _organisationRepository.GetByIdAsync(orgId, org => org.ChildOrganisations);
        if (organisation is null)
            throw new InvalidDataException($"Could not find organisation with id {orgId}");

        List<Guid> orgIds = new List<Guid>() { organisation.Id };
        if (organisation.ChildOrganisations.Any())
        {
            orgIds.AddRange(organisation.ChildOrganisations.Select(co => co.Id));
        }

        var users = await _userService.GetUsersByOrganisationIdsAsync(orgIds);

        return users;
    }

    public async Task<bool> CanReadOrganisationAsync(Guid orgId)
    {
        var getOrgTask = _organisationRepository.GetAllAsync(org => org.Id == orgId || org.ParentOrgId == orgId);
        var getUserTask = _userService.GetCurrentLoginUserAsync();

        await Task.WhenAll(new List<Task>() { getOrgTask, getUserTask });

        var orgIds = getOrgTask.Result.Select(org => org.Id);
        var currentUser = getUserTask.Result;

        return currentUser.OrganisationId.HasValue && orgIds.Contains(currentUser.OrganisationId.Value);
    }

    public Task<IEnumerable<Organisation>> GetSubOrganisations(Guid parentOrgId)
    {
        return _organisationRepository.GetAllAsync(org => org.ParentOrgId == parentOrgId);
    }

    public async Task<(List<Organisation> results, int totalCount)> SearchOrganisationAsync(string? searchTerm, int pageNumber, int pageSize, string sortBy, string sortDirection)
    {
        Expression<Func<Organisation, bool>> predicate = o => o.DiscardedAt == null;

        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
           predicate = predicate.And(o =>
                o.Name.Contains(searchTerm) ||
                o.StreetLine1.Contains(searchTerm) ||
                (o.StreetLine2 != null && o.StreetLine2.Contains(searchTerm)) ||
                o.State.Contains(searchTerm) ||
                o.Postcode.Contains(searchTerm) ||
                o.Suburb.Contains(searchTerm) ||
                (o.Service != null && o.Service.Contains(searchTerm)) ||
                (o.ClientGroup != null && o.ClientGroup.Contains(searchTerm)) ||
                (o.KeyContactName != null && o.KeyContactName.Contains(searchTerm)) ||
                (o.KeyContactPhone != null && o.KeyContactPhone.Contains(searchTerm)) ||
                (o.KeyContactEmail != null && o.KeyContactEmail.Contains(searchTerm)));
        }

        // Fetch queryable from repository
        var query = await _organisationRepository.GetAllAsync(predicate, o => o.ParentOrg);

        // Get total count before pagination
        var totalCount = query.Count();

        // Apply sorting
        query = ApplySorting(query.AsQueryable(), sortBy, sortDirection);

        // Apply pagination
        var results = query
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToList();

        return (results, totalCount);
    }

    private IQueryable<Organisation> ApplySorting(IQueryable<Organisation> query, string sortBy, string sortDirection)
    {
        var isDescending = sortDirection.Equals("desc", StringComparison.OrdinalIgnoreCase);

        return sortBy.ToLower() switch
        {
            "name" => isDescending ? query.OrderByDescending(o => o.Name) : query.OrderBy(o => o.Name),
            "state" => isDescending ? query.OrderByDescending(o => o.State) : query.OrderBy(o => o.State),
            "suburb" => isDescending ? query.OrderByDescending(o => o.Suburb) : query.OrderBy(o => o.Suburb),
            "postcode" => isDescending ? query.OrderByDescending(o => o.Postcode) : query.OrderBy(o => o.Postcode),
            _ => query.OrderBy(o => o.Name) // Default sort
        };
    }

}
