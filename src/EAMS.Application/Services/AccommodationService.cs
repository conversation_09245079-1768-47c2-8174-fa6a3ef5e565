using AutoMapper;
using EAMS.Application.DTOs;
using EAMS.Application.Interfaces;
using EAMS.Domain.Aggregates;
using EAMS.Domain.Exceptions;
using EAMS.Domain.Repositories;
using EAMS.Domain.ValueObjects;
using System.Linq.Expressions;

namespace EAMS.Application.Services;

public class AccommodationService : IAccommodationService
{
    private readonly IAccommodationRepository _accommodationRepository;
    private readonly IMapper _mapper;
    private readonly IRepository<AccommodationType, int> _accommodationTypeRepository;
    private readonly IRepository<Density, int> _densityRepository;
    private readonly IRepository<Duration, int> _durationRepository;
    private readonly IRepository<Region, int> _regionRepository;

    public AccommodationService(
        IAccommodationRepository accommodationRepository,
        IMapper mapper,
        IRepository<AccommodationType, int> accommodationTypeRepository,
        IRepository<Density, int> densityRepository,
        IRepository<Duration, int> durationRepository,
        IRepository<Region, int> regionRepository)
    {
        _accommodationRepository = accommodationRepository;
        _mapper = mapper;
        _accommodationTypeRepository = accommodationTypeRepository;
        _densityRepository = densityRepository;
        _durationRepository = durationRepository;
        _regionRepository = regionRepository;
    }

    public async Task<IEnumerable<AccommodationDto>> GetAll()
    {
        var accommodations = await _accommodationRepository.GetAllAsync();
        return _mapper.Map<IEnumerable<AccommodationDto>>(accommodations);
    }

    public async Task<AccommodationDto?> GetById(long id)
    {
        var accommodation = await _accommodationRepository.GetByIdAsync(id);
        return accommodation != null ? _mapper.Map<AccommodationDto>(accommodation) : null;
    }

    public async Task<AccommodationDto> Create(AccommodationDto accommodationDto)
    {
        var regionExists = await _regionRepository.GetByIdAsync(accommodationDto.RegionId);
        if (regionExists == null)
        {
            throw new EntityNotFoundException("Region", accommodationDto.RegionId);
        }

        var accommodationTypeExists = await _accommodationTypeRepository.GetByIdAsync(accommodationDto.AccommodationTypeId);
        if (accommodationTypeExists == null)
        {
            throw new EntityNotFoundException("AccommodationType", accommodationDto.AccommodationTypeId);
        }

        var densityExists = await _densityRepository.GetByIdAsync(accommodationDto.DensityId);
        if (densityExists == null)
        {
            throw new EntityNotFoundException("Density", accommodationDto.DensityId);
        }

        foreach (var durationId in accommodationDto.DurationIds)
        {
            var durationExists = await _durationRepository.GetByIdAsync(durationId);
            if (durationExists == null)
            {
                throw new EntityNotFoundException("Duration", durationId);
            }
        }

        var accommodation = _mapper.Map<Accommodation>(accommodationDto);

        // Set timestamps for new entity
        accommodation.CreatedAt = DateTime.UtcNow;
        accommodation.UpdatedAt = DateTime.UtcNow;

        await _accommodationRepository.AddAsync(accommodation);
        return _mapper.Map<AccommodationDto>(accommodation);
    }

    public async Task<AccommodationDto> Update(AccommodationDto accommodationDto)
    {
        // Check if accommodation exists first
        var existingAccommodation = await _accommodationRepository.GetByIdAsync(accommodationDto.Id);
        if (existingAccommodation == null)
        {
            throw new EntityNotFoundException("Accommodation", accommodationDto.Id);
        }

        var regionExists = await _regionRepository.GetByIdAsync(accommodationDto.RegionId);
        if (regionExists == null)
        {
            throw new EntityNotFoundException("Region", accommodationDto.RegionId);
        }

        var accommodationTypeExists = await _accommodationTypeRepository.GetByIdAsync(accommodationDto.AccommodationTypeId);
        if (accommodationTypeExists == null)
        {
            throw new EntityNotFoundException("AccommodationType", accommodationDto.AccommodationTypeId);
        }

        var densityExists = await _densityRepository.GetByIdAsync(accommodationDto.DensityId);
        if (densityExists == null)
        {
            throw new EntityNotFoundException("Density", accommodationDto.DensityId);
        }

        foreach (var durationId in accommodationDto.DurationIds)
        {
            var durationExists = await _durationRepository.GetByIdAsync(durationId);
            if (durationExists == null)
            {
                throw new EntityNotFoundException("Duration", durationId);
            }
        }

        var accommodation = _mapper.Map<Accommodation>(accommodationDto);

        // Update timestamp
        accommodation.UpdatedAt = DateTime.UtcNow;
        // Preserve original creation timestamp
        accommodation.CreatedAt = existingAccommodation.CreatedAt;

        await _accommodationRepository.UpdateAsync(accommodation);
        return _mapper.Map<AccommodationDto>(accommodation);
    }

    public async Task<bool> Delete(long id)
    {
        // Check if accommodation exists first
        var exists = await _accommodationRepository.GetByIdAsync(id);
        if (exists == null)
        {
            return false;
        }

        await _accommodationRepository.DeleteAsync(id);
        return true;
    }
}
