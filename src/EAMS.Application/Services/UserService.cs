﻿using EAMS.Application.Interfaces;
using EAMS.Domain.Entities;
using EAMS.Domain.Interfaces;
using EAMS.Domain.Repositories;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using GraphUser = Microsoft.Graph.Models.User;
using User = EAMS.Domain.Aggregates.User;

namespace EAMS.Application.Services;

public class UserService : IUserService
{
    private readonly IGraphService _graphService;
    private readonly IUserRepository _userRepository;
    private readonly IUserInvitationRepository _userInvitationRepository;
    private readonly IOrganisationRepository _organisationRepository;
    private readonly ILogger<UserService> _logger;
    private readonly IConfiguration _configuration;

    public UserService(IGraphService graphClient,
        IUserRepository userRepository,
        IUserInvitationRepository userInvitationRepository,
        IOrganisationRepository organisationRepository,
        IConfiguration configuration,
        ILogger<UserService> logger)
    {
        _graphService = graphClient;
        _userRepository = userRepository;
        _userInvitationRepository = userInvitationRepository;
        _organisationRepository = organisationRepository;
        _logger = logger;
        _configuration = configuration;
    }

    public async Task<User?> GetCurrentLoginUserAsync()
    {
        var graphUser = await _graphService.GetCurrentLoginUserAsync();

        if (graphUser is not null && !string.IsNullOrEmpty(graphUser.Id))
        {
            var userId = Guid.Parse(graphUser.Id);
            var user = await _userRepository.GetByIdAsync(userId, usr => usr.Organisation);

            if (user is not null)
            {
                graphUser.CompanyName = user.Organisation?.Name;
                user.GraphUser = graphUser;
                return user;
            }
            else
            {
                // check if the any invitation for this user using query invitedUserId == userId, include target organisation navigation object
                var invitationResults = await _userInvitationRepository
                    .GetAllAsync(ui => ui.InvitedUserId == userId, ui => ui.TargetOrganisation);
                var existingInvitation = invitationResults.FirstOrDefault();

                // create a new user in the database.
                var newUser = new User
                {
                    Id = userId,
                    OrganisationId = existingInvitation?.TargetOrganisationId ?? null,
                };

                await _userRepository.AddAsync(newUser);
                newUser.GraphUser = graphUser;
                return newUser;
            }
        }

        return null;
    }

    public async Task<User?> GetUserByIdAsync(Guid userId)
    {

        var graphUser = await _graphService.GetUserByIdAsync(userId);
        if (graphUser is not null)
        {
            return await this.LoadGraphUserRecordAsync(graphUser);
        }

        return null;
    }


    public async Task<UserInvitation?> CreateInvitationAsync(UserInvitation userInvitation)
    {
        // check if the targetOrganisationId exists
        var organisation = await _organisationRepository.GetByIdAsync(userInvitation.TargetOrganisationId.Value);
        if (organisation == null)
            throw new InvalidDataException("TargetOrganisationId does not exists");

        // Convert newInvitation into Invitation and call GraphService.CreateInvitation
        var invitation =
            await _graphService.InviteUserAsync(userInvitation.InvitedUserEmailAddress, userInvitation.InviteRedirectUrl);

        // Convert invitation to UserInvitation class update Invitation to database
        if (invitation is not null && !string.IsNullOrEmpty(invitation.Id))
        {
            userInvitation.Id = Guid.Parse(invitation.Id);
            userInvitation.InvitedUserId = !string.IsNullOrEmpty(invitation.InvitedUser?.Id)
                ? Guid.Parse(invitation.InvitedUser.Id)
                : Guid.Empty;
            var currentUser = await this.GetCurrentLoginUserAsync();
            userInvitation.InvitedByUserId = currentUser?.Id ?? Guid.Empty;

            // check if invitation exists
            var existingInvitation = await _userInvitationRepository.GetByIdAsync(userInvitation.Id);
            if (existingInvitation is null)
            {
                // Add new user to groups.
                var invitedToGroups = userInvitation.Roles.Split(',');
                foreach (var groupName in invitedToGroups)
                {
                    var group = await _graphService.GetGroupByNameAsync(groupName);
                    if (group is not null)
                    {
                        await _graphService.AddUserToGroupAsync(Guid.Parse(invitation.InvitedUser.Id), Guid.Parse(group.Id));
                    }
                }

                // record invitation
                await _userInvitationRepository.AddAsync(userInvitation);
            }
            else
            {
                // just send another invite and update the record
                await _userInvitationRepository.UpdateAsync(userInvitation);
            }
        }

        return userInvitation;
    }

    public async Task<User?> UpdateUserDetailsAsync(GraphUser graphUser)
    {
        // check if the organisations exists
        var foundOrganisation = await _organisationRepository.GetOrganisationByNameAsync(graphUser.CompanyName);
        bool updateUserRecord = false;
        if (foundOrganisation is not null)
        {
            // check if organisationId is different from the current user's organisationId
            var userRecord = await _userRepository.GetByIdAsync(Guid.Parse(graphUser.Id));
            if (userRecord == null)
                throw new InvalidDataException("User does not exists");

            updateUserRecord = userRecord.OrganisationId != foundOrganisation.Id;
        }

        var updatedGraphUser = await _graphService.PatchUserDetailsAsync(graphUser);
        // Once the user details is updated in Azure AD, update the user record in the database if the organisationId is changed.
        if (updateUserRecord && foundOrganisation is not null)
        {
            var userRecord = await _userRepository.GetByIdAsync(Guid.Parse(graphUser.Id));
            if (userRecord != null)
            {
                userRecord.OrganisationId = foundOrganisation.Id;
                await _userRepository.UpdateAsync(userRecord);
            }

            return userRecord;
        }

        return new User
        {
            Id = Guid.Parse(updatedGraphUser.Id),
            GraphUser = updatedGraphUser,
        };
    }

    public async Task AddUserToGroupAsync(Guid userId, string groupName)
    {
        // create a task list and execute them in parallel
        var targetUserTask = _graphService.GetUserByIdAsync(userId);
        var groupTask = _graphService.GetGroupByNameAsync(groupName);
        var tasks = new List<Task>() { targetUserTask, groupTask };
        await Task.WhenAll(tasks);

        if (targetUserTask.Result is null)
            throw new InvalidDataException($"Could not find user with Id: {userId}");

        if (groupTask.Result is null)
            throw new InvalidDataException($"Could not find group with name: {groupName}");

        await _graphService.AddUserToGroupAsync(Guid.Parse(targetUserTask.Result.Id), Guid.Parse(groupTask.Result.Id));
    }

    public async Task RemoveUserFromGroupAsync(Guid userId, string groupName)
    {
        // create a task list and execute them in parallel
        var targetUserTask = _graphService.GetUserByIdAsync(userId);
        var groupTask = _graphService.GetGroupByNameAsync(groupName);
        var tasks = new List<Task>() { targetUserTask, groupTask };
        await Task.WhenAll(tasks);

        if (targetUserTask.Result is null)
            throw new InvalidDataException($"Could not find user with Id: {userId}");

        if (groupTask.Result is null)
            throw new InvalidDataException($"Could not find group with name: {groupName}");

        await _graphService.RemoveUserFromGroupAsync(Guid.Parse(targetUserTask.Result.Id), Guid.Parse(groupTask.Result.Id));
    }


    public async Task<List<User>> GetUsersByOrganisationIdsAsync(List<Guid> orgIds)
    {
        // find users with orgIds
        var users = await _userRepository.GetAllAsync(u => u.OrganisationId != null && orgIds.Contains(u.OrganisationId.Value), u => u.Organisation);
        var graphUsers = await _graphService.GetUsersByIdsAsync(users.Select(u => u.Id).ToList());

        graphUsers.ForEach(gu =>
        {
            var user = users.FirstOrDefault(u => u.Id.Equals(Guid.Parse(gu.Id)));
            if (user is not null)
                user.GraphUser = gu;
        });

        return users.Where(u => { return u.GraphUser is not null; }).ToList();
    }

    public async Task<List<User>> GetAllUsersAsync()
    {
        var usersGroupName = _configuration.GetSection("GraphApi:RolesMapping:Users").Value;
        var graphUsers = await _graphService.GetUsersInGroupAsync(usersGroupName);

        if (graphUsers is not null && graphUsers.Any())
        {
            var sortedUsers = graphUsers.OrderBy(gu => gu.DisplayName);
            var dbUsers = await _userRepository.GetAllAsync(null, usr => usr.Organisation);
            return sortedUsers.Select(gu =>
            {
                var dbUser = dbUsers.FirstOrDefault(u => u.Id == Guid.Parse(gu.Id));
                if (dbUser is not null)
                {
                    dbUser.GraphUser = gu;
                    return dbUser;
                }
                else
                {
                    return new User
                    {
                        Id = Guid.Parse(gu.Id),
                        GraphUser = gu
                    };
                }
            }).ToList();
        }

        return null;
    }

    public async Task<User?> GetUserByEmailAsync(string email)
    {
        var graphUser = await _graphService.GetUserByEmailAsync(email);
        if (graphUser is not null)
        {
            return await this.LoadGraphUserRecordAsync(graphUser);
        }

        return null;
    }

    /// <summary>
    /// Get User Record by GraphUser obj.
    /// </summary>
    /// <param name="graphUser"></param>
    /// <returns></returns>
    private async Task<User> LoadGraphUserRecordAsync(GraphUser graphUser)
    {
        // get User record from database
        var userId = Guid.Parse(graphUser.Id);
        var user = await _userRepository.GetByIdAsync(userId, usr => usr.Organisation);

        if (user is not null)
        {
            graphUser.CompanyName = user.Organisation?.Name;
            user.GraphUser = graphUser;
            return user;
        }

        return null;
    }

    public async Task DeleteUserAsync(Guid userId)
    {
        await _graphService.DeleteUserAsync(userId);
    }

    public async Task<(List<User> results, int totalCount)> SearchUsersAsync(
        string? searchTerm, string? searchProperty,
        int? pageNumber, int? pageSize, string sortBy, string sortDirection)
    {
        string userGroup = _configuration.GetSection("GraphApi:RolesMapping:Users").Value;
        
        var (graphUsers, totalCount) = await _graphService.SearchAndSortUsersInGroupAsync(userGroup,
            searchTerm, searchProperty, sortBy, sortDirection, pageNumber ?? 1, pageSize ?? 50);
        if (graphUsers is not null && graphUsers.Any())
        {
            // var users = graphUsers.Select(gu => new User
            // {
            //     Id = Guid.Parse(gu.Id),
            //     GraphUser = gu
            // });
            
            var userIds = graphUsers.Select(gu => Guid.Parse(gu.Id)).ToList();
            var users = await _userRepository.GetAllAsync(u => userIds.Contains(u.Id), u => u.Organisation);
            foreach (var user in users)
            {
                var matchingGraphUser = graphUsers.FirstOrDefault(gu => gu.Id == user.Id.ToString());
                if (matchingGraphUser != null)
                {
                    matchingGraphUser.CompanyName = user.Organisation?.Name;
                    user.GraphUser = matchingGraphUser;
                }
            }

            return (users.ToList(), totalCount);
        }

        return (new List<User>(), totalCount);
    }
}